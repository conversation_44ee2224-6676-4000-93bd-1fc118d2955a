<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\HaiboService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class HaiboController extends Controller
{
    protected HaiboService $haiboService;

    public function __construct(HaiboService $haiboService)
    {
        $this->haiboService = $haiboService;
    }

    /**
     * 创建/修改配送商门店接口
     *
     * POST /api/haibo/store
     * Content-Type: application/x-www-form-urlencoded
     */
    public function createOrUpdateStore(Request $request)
    {
        try {
            // 记录海博请求信息
            Log::channel('haibo')->info('海博创建/修改配送商门店请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数 - 根据海博接口规范
            $validator = Validator::make($request->all(), [
                'contactPhone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            ], [
                'contactPhone.required' => '门店联系人电话不能为空',
                'contactPhone.regex' => '手机号格式不正确',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博请求参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => 1, // 海博规范：非0表示失败
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理业务逻辑
            $result = $this->haiboService->createOrUpdateStore($request->all());

            Log::channel('haibo')->info('海博门店操作成功', $result);

            // 按照海博接口规范返回响应
            return response()->json([
                'code' => 0, // 海博规范：0表示成功
                'message' => '成功',
                'data' => [
                    'carrierShopId' => $result['data']['carrier_shop_id'],
                ]
            ]);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博门店操作异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => 1, // 海博规范：非0表示失败
                'message' => '操作失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 询价接口 - 获取预估配送费、配送时间等信息
     *
     * POST /api/haibo/valuating
     * Content-Type: application/json
     */
    public function valuating(Request $request)
    {
        try {
            // 记录海博询价请求信息
            Log::channel('haibo')->info('海博询价请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required', // 平台订单号
                'recipientLng' => 'required', // 收件人经度
                'recipientLat' => 'required',// 收件人纬度
                'senderLng' => 'required', // 发件人经度
                'senderLat' => 'required',// 发件人纬度
            ], [
                'orderId.required' => '海博平台订单号不能为空',
                'recipientLng.required' => '收件人经度不能为空',
                'recipientLat.required' => '收件人纬度不能为空',
                'senderLng.required' => '发件人经度不能为空',
                'senderLat.required' => '发件人纬度不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博询价参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 按照 maiyatian 的实现模式处理询价逻辑
            $result = $this->haiboService->valuatingWithOrderLookup($request->all());

            Log::channel('haibo')->info('海博询价处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博询价异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 发单接口 - 创建配送订单
     *
     * POST /api/haibo/send
     * Content-Type: application/json
     */
    public function send(Request $request)
    {
        try {
            // 记录海博发单请求信息
            Log::channel('haibo')->info('海博发单请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required|string|min:18|max:45',
                'recipientLng' => 'required|integer',
                'recipientLat' => 'required|integer',
                'senderLng' => 'required|integer',
                'senderLat' => 'required|integer',
            ], [
                'orderId.required' => '海博平台订单号不能为空',
                'recipientLng.required' => '收件人经度不能为空',
                'recipientLat.required' => '收件人纬度不能为空',
                'senderLng.required' => '发件人经度不能为空',
                'senderLat.required' => '发件人纬度不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博发单参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 检查订单是否已存在
            $existingOrder = \App\Models\O2oErrandOrder::where('out_order_no', $request->input('orderId'))
                ->where('app_key', \App\Models\O2oErrandOrder::APP_KEY_HB)
                ->first();

            if ($existingOrder) {
                Log::channel('haibo')->warning('海博订单已存在', [
                    'order_id' => $request->input('orderId'),
                    'existing_order_no' => $existingOrder->order_no
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '订单已存在',
                    'data' => null
                ]);
            }

            // 调用服务处理发单逻辑
            $result = $this->haiboService->send($request->all());

            Log::channel('haibo')->info('海博发单处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博发单异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 取消订单接口
     *
     * POST /api/haibo/cancel
     * Content-Type: application/json
     */
    public function cancel(Request $request)
    {
        try {
            // 记录海博取消订单请求信息
            Log::channel('haibo')->info('海博取消订单请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required|string|min:18|max:45',
                'cancelReasonCode' => 'required|integer',
                'cancelReasonDesc' => 'required|string|max:256',
            ], [
                'orderId.required' => '海博平台订单号不能为空',
                'orderId.min' => '订单号长度不能少于18个字符',
                'orderId.max' => '订单号长度不能超过45个字符',
                'cancelReasonCode.required' => '取消原因code不能为空',
                'cancelReasonCode.integer' => '取消原因code必须为整数',
                'cancelReasonDesc.required' => '取消原因说明不能为空',
                'cancelReasonDesc.max' => '取消原因说明不能超过256个字符',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博取消订单参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理取消订单逻辑
            $result = $this->haiboService->cancelOrder($request->all());

            Log::channel('haibo')->info('海博取消订单处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博取消订单异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

}
