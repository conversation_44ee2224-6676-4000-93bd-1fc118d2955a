<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\HaiboService;
use App\Models\Site;
use App\Models\Pricing;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;

class HaiboValuatingTest extends TestCase
{
    use RefreshDatabase;

    protected HaiboService $haiboService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->haiboService = new HaiboService();
    }

    /**
     * 测试基本询价功能
     */
    public function test_basic_valuating()
    {
        // 创建测试站点
        $site = Site::factory()->create([
            'name' => '测试站点',
            'farthest' => 10000, // 10公里
            'status' => 1
        ]);

        // 创建测试定价
        Pricing::factory()->create([
            'type' => Pricing::TYPE_USER,
            'site_id' => $site->id,
            'base_price' => 5.00,
            'is_special' => false
        ]);

        $testData = [
            'tradeOrderSource' => 4,
            'orderId' => 'HB' . time(),
            'serviceCode' => 'standard_delivery',
            'recipientName' => '张三',
            'recipientPhone' => '13800138001',
            'recipientAddress' => '北京市朝阳区建国路88号',
            'recipientLng' => 116447552, // 北京坐标
            'recipientLat' => 39906901,
            'prebook' => 0, // 即时单
            'totalWeight' => 1500, // 1.5kg
            'carrierMerchantId' => 'TEST_MERCHANT',
            'senderLng' => 116398419,
            'senderLat' => 39908722,
            'senderName' => '测试门店',
            'senderContract' => '13800138000',
            'senderAddressDetail' => '北京市朝阳区测试地址'
        ];

        $response = $this->postJson('/api/haibo/valuating', $testData);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals(HaiboService::RESULT_SUCCESS, $responseData['code']);
        $this->assertEquals('成功', $responseData['message']);
        $this->assertArrayHasKey('data', $responseData);
        
        $data = $responseData['data'];
        $this->assertArrayHasKey('predictDeliveryTime', $data);
        $this->assertArrayHasKey('actualFee', $data);
        $this->assertArrayHasKey('deliveryFee', $data);
        $this->assertArrayHasKey('deliveryDistance', $data);
        $this->assertArrayHasKey('discountFee', $data);
        $this->assertArrayHasKey('insuredFee', $data);
    }

    /**
     * 测试预约单询价
     */
    public function test_scheduled_valuating()
    {
        $site = Site::factory()->create([
            'farthest' => 10000,
            'status' => 1
        ]);

        Pricing::factory()->create([
            'type' => Pricing::TYPE_USER,
            'site_id' => $site->id,
            'base_price' => 5.00,
            'is_special' => false
        ]);

        $testData = [
            'tradeOrderSource' => 4,
            'orderId' => 'HB' . time(),
            'serviceCode' => 'standard_delivery',
            'recipientName' => '李四',
            'recipientPhone' => '13800138002',
            'recipientAddress' => '北京市海淀区中关村大街1号',
            'recipientLng' => 116310003,
            'recipientLat' => 39992492,
            'prebook' => 1, // 预约单
            'expectedDeliveryTime' => time() + 3600, // 1小时后
            'totalWeight' => 2000,
            'carrierMerchantId' => 'TEST_MERCHANT',
            'senderLng' => 116398419,
            'senderLat' => 39908722,
            'senderName' => '测试门店',
            'senderContract' => '13800138000',
            'senderAddressDetail' => '北京市朝阳区测试地址'
        ];

        $response = $this->postJson('/api/haibo/valuating', $testData);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals(HaiboService::RESULT_SUCCESS, $responseData['code']);
        
        // 预约单应该返回期望的送达时间
        $this->assertEquals($testData['expectedDeliveryTime'], $responseData['data']['predictDeliveryTime']);
    }

    /**
     * 测试保价询价
     */
    public function test_insured_valuating()
    {
        $site = Site::factory()->create([
            'farthest' => 10000,
            'status' => 1
        ]);

        Pricing::factory()->create([
            'type' => Pricing::TYPE_USER,
            'site_id' => $site->id,
            'base_price' => 5.00,
            'is_special' => false
        ]);

        $testData = [
            'tradeOrderSource' => 4,
            'orderId' => 'HB' . time(),
            'serviceCode' => 'standard_delivery',
            'recipientName' => '王五',
            'recipientPhone' => '13800138003',
            'recipientAddress' => '北京市西城区西单大街1号',
            'recipientLng' => 116366794,
            'recipientLat' => 39915309,
            'prebook' => 0,
            'insuredMark' => 1, // 保价
            'totalValue' => 200.00, // 货品价值200元
            'totalWeight' => 1000,
            'carrierMerchantId' => 'TEST_MERCHANT',
            'senderLng' => 116398419,
            'senderLat' => 39908722,
            'senderName' => '测试门店',
            'senderContract' => '13800138000',
            'senderAddressDetail' => '北京市朝阳区测试地址'
        ];

        $response = $this->postJson('/api/haibo/valuating', $testData);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals(HaiboService::RESULT_SUCCESS, $responseData['code']);
        
        // 保价订单应该有保价费
        $this->assertGreaterThan(0, $responseData['data']['insuredFee']);
        
        // 实际支付金额应该包含保价费
        $expectedActualFee = $responseData['data']['deliveryFee'] + $responseData['data']['insuredFee'] - $responseData['data']['discountFee'];
        $this->assertEquals($expectedActualFee, $responseData['data']['actualFee']);
    }

    /**
     * 测试参数验证 - 缺少必填参数
     */
    public function test_missing_required_params()
    {
        $testData = [
            'tradeOrderSource' => 4,
            // 缺少 orderId
            'serviceCode' => 'standard_delivery',
        ];

        $response = $this->postJson('/api/haibo/valuating', $testData);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals(HaiboService::RESULT_PARAM_ERROR, $responseData['code']);
        $this->assertStringContains('参数验证失败', $responseData['message']);
    }

    /**
     * 测试参数验证 - 重量超限
     */
    public function test_weight_validation()
    {
        $testData = [
            'tradeOrderSource' => 4,
            'orderId' => 'HB' . time(),
            'serviceCode' => 'standard_delivery',
            'recipientName' => '张三',
            'recipientPhone' => '13800138001',
            'recipientAddress' => '北京市朝阳区建国路88号',
            'recipientLng' => 116447552,
            'recipientLat' => 39906901,
            'prebook' => 0,
            'totalWeight' => 60000, // 60kg，超过限制
            'carrierMerchantId' => 'TEST_MERCHANT',
            'senderLng' => 116398419,
            'senderLat' => 39908722,
            'senderName' => '测试门店',
            'senderContract' => '13800138000',
            'senderAddressDetail' => '北京市朝阳区测试地址'
        ];

        $response = $this->postJson('/api/haibo/valuating', $testData);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals(HaiboService::RESULT_SYSTEM_ERROR, $responseData['code']);
        $this->assertStringContains('重量应在1-50000克之间', $responseData['message']);
    }

    /**
     * 测试预约时间验证 - 过去时间
     */
    public function test_past_delivery_time_validation()
    {
        $testData = [
            'tradeOrderSource' => 4,
            'orderId' => 'HB' . time(),
            'serviceCode' => 'standard_delivery',
            'recipientName' => '张三',
            'recipientPhone' => '13800138001',
            'recipientAddress' => '北京市朝阳区建国路88号',
            'recipientLng' => 116447552,
            'recipientLat' => 39906901,
            'prebook' => 1, // 预约单
            'expectedDeliveryTime' => time() - 3600, // 1小时前（过去时间）
            'totalWeight' => 1500,
            'carrierMerchantId' => 'TEST_MERCHANT',
            'senderLng' => 116398419,
            'senderLat' => 39908722,
            'senderName' => '测试门店',
            'senderContract' => '13800138000',
            'senderAddressDetail' => '北京市朝阳区测试地址'
        ];

        $response = $this->postJson('/api/haibo/valuating', $testData);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals(HaiboService::RESULT_SYSTEM_ERROR, $responseData['code']);
        $this->assertStringContains('不能是过去时间', $responseData['message']);
    }
}
