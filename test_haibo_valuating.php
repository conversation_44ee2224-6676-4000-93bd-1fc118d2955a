<?php

/**
 * 海博询价API测试脚本
 * 
 * 使用方法：
 * php test_haibo_valuating.php
 */

// 测试配置
$baseUrl = 'http://localhost:8000'; // 根据实际情况修改
$apiUrl = $baseUrl . '/api/haibo/valuating';

// 测试数据 - 即时单询价
$testDataImmediate = [
    'tradeOrderSource' => 4, // 海博自营
    'tradeOrderId' => '', // 可选
    'orderId' => 'HB' . time() . rand(1000, 9999),
    'serviceCode' => 'standard_delivery',
    'recipientName' => '张三',
    'recipientPhone' => '13800138001',
    'recipientAddress' => '北京市朝阳区建国路88号SOHO现代城',
    'recipientLng' => 116447552, // 火星坐标 * 10^6
    'recipientLat' => 39906901,  // 火星坐标 * 10^6
    'prebook' => 0, // 即时单
    'expectedDeliveryTime' => null,
    'expectedLeftDeliveryTime' => null,
    'expectedRightDeliveryTime' => null,
    'expectedPickupTime' => null,
    'insuredMark' => 0, // 不保价
    'totalValue' => 50.00,
    'totalWeight' => 1500, // 1.5kg，单位为克
    'totalVolume' => 2000, // 2000cm³
    'riderPickMethod' => 0, // 从商家门店到用户
    'goodsDetails' => json_encode([
        [
            'count' => 2,
            'name' => '麻辣烫',
            'price' => 15.00,
            'unit' => '份',
            'specs' => ['中辣', '加蛋']
        ],
        [
            'count' => 1,
            'name' => '奶茶',
            'price' => 20.00,
            'unit' => '杯',
            'specs' => ['半糖', '去冰']
        ]
    ]),
    'carrierMerchantId' => 'HAIBO_MERCHANT_001',
    'extInfo' => json_encode(['note' => '测试订单']),
    'carrierShopId' => 'HB_1',
    'senderLng' => 116398419, // 火星坐标 * 10^6
    'senderLat' => 39908722,  // 火星坐标 * 10^6
    'senderName' => '海博测试门店',
    'senderContract' => '13800138000',
    'senderAddressDetail' => '北京市朝阳区广顺北大街666号',
    'carModelCode' => '',
    'category' => 1 // 餐饮美食
];

// 测试数据 - 预约单询价
$testDataScheduled = [
    'tradeOrderSource' => 4, // 海博自营
    'tradeOrderId' => '', // 可选
    'orderId' => 'HB' . time() . rand(1000, 9999),
    'serviceCode' => 'standard_delivery',
    'recipientName' => '李四',
    'recipientPhone' => '13800138002',
    'recipientAddress' => '北京市海淀区中关村大街1号',
    'recipientLng' => 116310003, // 火星坐标 * 10^6
    'recipientLat' => 39992492,  // 火星坐标 * 10^6
    'prebook' => 1, // 预约单
    'expectedDeliveryTime' => time() + 3600, // 1小时后送达
    'expectedLeftDeliveryTime' => time() + 3300, // 55分钟后
    'expectedRightDeliveryTime' => time() + 3900, // 65分钟后
    'expectedPickupTime' => time() + 1800, // 30分钟后取货
    'insuredMark' => 1, // 保价
    'totalValue' => 200.00,
    'totalWeight' => 3000, // 3kg，单位为克
    'totalVolume' => 5000, // 5000cm³
    'riderPickMethod' => 0, // 从商家门店到用户
    'goodsDetails' => json_encode([
        [
            'count' => 1,
            'name' => '生日蛋糕',
            'price' => 180.00,
            'unit' => '个',
            'specs' => ['8寸', '巧克力味']
        ],
        [
            'count' => 1,
            'name' => '鲜花',
            'price' => 20.00,
            'unit' => '束',
            'specs' => ['玫瑰花']
        ]
    ]),
    'carrierMerchantId' => 'HAIBO_MERCHANT_001',
    'extInfo' => json_encode(['note' => '预约配送测试']),
    'carrierShopId' => 'HB_1',
    'senderLng' => 116398419, // 火星坐标 * 10^6
    'senderLat' => 39908722,  // 火星坐标 * 10^6
    'senderName' => '海博测试门店',
    'senderContract' => '13800138000',
    'senderAddressDetail' => '北京市朝阳区广顺北大街666号',
    'carModelCode' => '',
    'category' => 6 // 烘焙蛋糕
];

/**
 * 发送HTTP POST请求
 */
function sendRequest($url, $data) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => $httpCode];
    }
    
    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

/**
 * 打印测试结果
 */
function printResult($testName, $result) {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "测试: {$testName}\n";
    echo str_repeat("=", 50) . "\n";
    
    if (isset($result['error'])) {
        echo "❌ 请求失败: {$result['error']}\n";
        return;
    }
    
    echo "HTTP状态码: {$result['http_code']}\n";
    
    if ($result['http_code'] == 200) {
        $response = $result['response'];
        echo "✅ 请求成功\n";
        echo "返回结果:\n";
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        if (isset($response['code']) && $response['code'] == 0) {
            echo "✅ 询价成功\n";
            if (isset($response['data'])) {
                $data = $response['data'];
                echo "配送费: {$data['deliveryFee']}元\n";
                echo "实际支付: {$data['actualFee']}元\n";
                echo "配送距离: {$data['deliveryDistance']}米\n";
                echo "预计送达: " . date('Y-m-d H:i:s', $data['predictDeliveryTime']) . "\n";
            }
        } else {
            echo "❌ 询价失败: {$response['message']}\n";
        }
    } else {
        echo "❌ HTTP请求失败\n";
        echo "响应内容: " . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
}

// 执行测试
echo "开始海博询价API测试...\n";
echo "API地址: {$apiUrl}\n";

// 测试1: 即时单询价
$result1 = sendRequest($apiUrl, $testDataImmediate);
printResult("即时单询价", $result1);

// 测试2: 预约单询价
$result2 = sendRequest($apiUrl, $testDataScheduled);
printResult("预约单询价", $result2);

// 测试3: 参数错误测试
$testDataError = $testDataImmediate;
unset($testDataError['recipientName']); // 移除必填参数
$result3 = sendRequest($apiUrl, $testDataError);
printResult("参数错误测试", $result3);

echo "\n测试完成!\n";
