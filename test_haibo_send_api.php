<?php

/**
 * 海博发单API测试脚本
 * 
 * 使用方法：
 * php test_haibo_send_api.php
 */

// 测试配置
$baseUrl = 'http://localhost:8000'; // 根据实际情况修改

echo "=== 海博发单API测试 ===\n";

// 第一步：测试询价接口
echo "\n=== 第一步：测试询价接口 ===\n";
$valuatingUrl = $baseUrl . '/api/haibo/valuating';

$valuatingData = [
    'orderId' => 'HB_ORDER_' . time(),
    'recipientLng' => 116398419, // 火星坐标，坐标 * 10^6
    'recipientLat' => 39908722,  // 火星坐标，坐标 * 10^6
    'senderLng' => 116398419,    // 火星坐标，坐标 * 10^6
    'senderLat' => 39908722,     // 火星坐标，坐标 * 10^6
    'carrierMerchantId' => 'HB_1', // 假设已有的商家ID
    'totalWeight' => 1000, // 1000克
    'prebook' => 0, // 即时单
];

echo "询价URL: $valuatingUrl\n";
echo "询价数据: " . json_encode($valuatingData, JSON_UNESCAPED_UNICODE) . "\n";

$response = sendRequest($valuatingUrl, $valuatingData);
echo "询价响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";

// 第二步：测试发单接口
echo "\n=== 第二步：测试发单接口 ===\n";
$sendUrl = $baseUrl . '/api/haibo/send';

$sendData = [
    'tradeOrderSource' => 4, // 海博自营
    'tradeOrderId' => 'TRADE_' . time(),
    'orderSequence' => 'SEQ_' . time(),
    'orderId' => 'HB_ORDER_' . time() . '_SEND',
    'serviceCode' => 'STANDARD',
    'preDeliveryFee' => 8.50,
    'recipientName' => '张三',
    'recipientPhone' => '13800138001',
    'recipientAddress' => '北京市朝阳区广顺北大街666号',
    'recipientLng' => 116398419,
    'recipientLat' => 39908722,
    'prebook' => 0, // 即时单
    'expectedDeliveryTime' => 0,
    'expectedLeftDeliveryTime' => 0,
    'expectedRightDeliveryTime' => 0,
    'expectedPickupTime' => 0,
    'insuredMark' => 0, // 不保价
    'totalValue' => 50.00,
    'totalWeight' => 1000, // 1000克
    'totalVolume' => 1000, // 1000立方厘米
    'riderPickMethod' => 0, // 从商家门店到用户
    'tipFee' => 2.00,
    'goodsDetails' => json_encode([
        [
            'count' => 1,
            'name' => '测试商品',
            'price' => 50.00,
            'unit' => '个',
            'specs' => ['规格1', '规格2']
        ]
    ]),
    'orderRemark' => '测试订单备注',
    'shopRemark' => '测试商户备注',
    'carrierShopId' => 'HB_SHOP_1',
    'senderLng' => 116398419,
    'senderLat' => 39908722,
    'senderName' => '测试商家',
    'senderContract' => '13800138000',
    'senderAddressDetail' => '北京市朝阳区测试商家地址',
    'operatorName' => '操作员',
    'operatorContract' => '13800138002',
    'carrierMerchantId' => 'HB_1',
    'extInfo' => json_encode(['test' => 'data']),
    'carModelCode' => '',
    'riderInfo' => '',
    'category' => 1, // 餐饮美食
];

echo "发单URL: $sendUrl\n";
echo "发单数据: " . json_encode($sendData, JSON_UNESCAPED_UNICODE) . "\n";

$response = sendRequest($sendUrl, $sendData);
echo "发单响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";

// 分析响应结果
if (isset($response['code'])) {
    if ($response['code'] === 0) {
        echo "\n✅ 发单成功！\n";
        if (isset($response['data'])) {
            echo "订单信息:\n";
            foreach ($response['data'] as $key => $value) {
                echo "  $key: $value\n";
            }
        }
    } else {
        echo "\n❌ 发单失败！\n";
        echo "错误信息: " . ($response['message'] ?? '未知错误') . "\n";
    }
} else {
    echo "\n❌ 响应格式错误\n";
}

echo "\n=== 测试完成 ===\n";

/**
 * 发送HTTP请求
 */
function sendRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => $httpCode];
    }
    
    $decoded = json_decode($response, true);
    if ($decoded === null) {
        return ['error' => 'Invalid JSON response', 'raw_response' => $response, 'http_code' => $httpCode];
    }
    
    return $decoded;
}
